[pytest]
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=55
    -v
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    models: marks tests as model tests
    services: marks tests as service tests
    core: marks tests as core functionality tests
    auth: marks tests as authentication tests
    inventory: marks tests as inventory tests
    sales: marks tests as sales tests
    permissions: marks tests as permission tests
    tasks: marks tests as task tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
